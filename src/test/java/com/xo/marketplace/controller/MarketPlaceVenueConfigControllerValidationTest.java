package com.xo.marketplace.controller;

import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;

class MarketPlaceVenueConfigControllerValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("Should return validation error when feeConfig has null required fields")
    void validateMarketPlaceVenueConfigDTO_WhenFeeConfigHasNullRequiredFields_ShouldReturnValidationError() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(null) // Required field is null
                .appFeeFixedPerTransaction(null) // Required field is null
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert
        assertThat(violations).isNotEmpty();
        assertThat(violations).hasSize(2); // appFeePerc and appFeeFixedPerTransaction are null
        
        assertThat(violations).anyMatch(v -> v.getMessage().equals("appFeePerc is required when feeConfig is provided"));
        assertThat(violations).anyMatch(v -> v.getMessage().equals("appFeeFixedPerTransaction is required when feeConfig is provided"));
    }

    @Test
    @DisplayName("Should return validation error when feeConfig has null passPspFees")
    void validateMarketPlaceVenueConfigDTO_WhenFeeConfigHasNullPassPspFees_ShouldReturnValidationError() {
        // Arrange
        VenueFeeConfigDTO invalidFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(new BigDecimal("0.05"))
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(null) // Required field is null
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(invalidFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert
        assertThat(violations).isNotEmpty();
        assertThat(violations).hasSize(1); // passPspFees is null
        
        assertThat(violations).anyMatch(v -> v.getMessage().equals("passPspFees is required when feeConfig is provided"));
    }

    @Test
    @DisplayName("Should succeed when feeConfig has all required fields")
    void validateMarketPlaceVenueConfigDTO_WhenFeeConfigHasAllRequiredFields_ShouldSucceed() {
        // Arrange
        VenueFeeConfigDTO validFeeConfig = VenueFeeConfigDTO.builder()
                .venueId("venue123")
                .appFeePerc(new BigDecimal("0.05"))
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(validFeeConfig)
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert
        assertThat(violations).isEmpty();
    }

    @Test
    @DisplayName("Should succeed when feeConfig is null")
    void validateMarketPlaceVenueConfigDTO_WhenFeeConfigIsNull_ShouldSucceed() {
        // Arrange
        MarketPlaceVenueConfigDTO configDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId("venue123")
                .visible(true)
                .feeConfig(null) // No fee config provided
                .build();

        // Act
        Set<ConstraintViolation<MarketPlaceVenueConfigDTO>> violations = validator.validate(configDTO);

        // Assert
        assertThat(violations).isEmpty();
    }
}
